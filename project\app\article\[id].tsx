import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  Image,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Linking,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, ExternalLink, Share, Bookmark, Clock, User } from 'lucide-react-native';
import { NewsArticle } from '@/types/news';
import { StorageService } from '@/services/storage';
import { useTheme } from '@/contexts/ThemeContext';

export default function ArticleScreen() {
  const { article: articleParam } = useLocalSearchParams();
  const [article, setArticle] = useState<NewsArticle | null>(null);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const { colors } = useTheme();

  useEffect(() => {
    if (articleParam && typeof articleParam === 'string') {
      try {
        const parsedArticle = JSON.parse(articleParam);
        setArticle(parsedArticle);
        checkBookmarkStatus(parsedArticle.url);
      } catch (error) {
        console.error('Error parsing article:', error);
        router.back();
      }
    }
  }, [articleParam]);

  const checkBookmarkStatus = async (articleUrl: string) => {
    const bookmarked = await StorageService.isBookmarked(articleUrl);
    setIsBookmarked(bookmarked);
  };

  const handleBookmark = async () => {
    if (!article) return;

    if (isBookmarked) {
      await StorageService.removeBookmark(article.url);
      setIsBookmarked(false);
    } else {
      const bookmarkedArticle = {
        ...article,
        bookmarkedAt: new Date().toISOString(),
      };
      await StorageService.addBookmark(bookmarkedArticle);
      setIsBookmarked(true);
    }
  };

  const handleOpenOriginal = () => {
    if (article?.url) {
      Linking.openURL(article.url);
    }
  };

  const handleShare = () => {
    // In a real app, you would use the Share API
    console.log('Share article:', article?.title);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (!article) {
    return null;
  }

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      padding: 8,
    },
    headerActions: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    actionButton: {
      padding: 8,
      marginLeft: 8,
    },
    imageContainer: {
      height: 250,
      backgroundColor: colors.border,
    },
    image: {
      width: '100%',
      height: '100%',
    },
    content: {
      padding: 16,
    },
    title: {
      fontSize: 28,
      fontFamily: 'Playfair-Bold',
      color: colors.text,
      lineHeight: 36,
      marginBottom: 16,
    },
    metadata: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
      flexWrap: 'wrap',
    },
    metadataItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: 16,
      marginBottom: 8,
    },
    metadataText: {
      fontSize: 14,
      fontFamily: 'Inter-Medium',
      color: colors.textSecondary,
      marginLeft: 6,
    },
    source: {
      fontSize: 16,
      fontFamily: 'Inter-SemiBold',
      color: colors.primary,
      marginBottom: 16,
    },
    description: {
      fontSize: 18,
      fontFamily: 'Inter-Medium',
      color: colors.text,
      lineHeight: 26,
      marginBottom: 20,
    },
    content_text: {
      fontSize: 16,
      fontFamily: 'Inter-Regular',
      color: colors.text,
      lineHeight: 24,
      marginBottom: 20,
    },
    readMoreContainer: {
      backgroundColor: colors.surface,
      borderRadius: 12,
      padding: 16,
      marginTop: 20,
      borderWidth: 1,
      borderColor: colors.border,
    },
    readMoreTitle: {
      fontSize: 16,
      fontFamily: 'Inter-SemiBold',
      color: colors.text,
      marginBottom: 8,
    },
    readMoreDescription: {
      fontSize: 14,
      fontFamily: 'Inter-Regular',
      color: colors.textSecondary,
      marginBottom: 12,
      lineHeight: 20,
    },
    readMoreButton: {
      backgroundColor: colors.primary,
      borderRadius: 8,
      paddingVertical: 12,
      paddingHorizontal: 16,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    readMoreButtonText: {
      fontSize: 14,
      fontFamily: 'Inter-SemiBold',
      color: '#ffffff',
      marginRight: 8,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
            <Share size={20} color={colors.text} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={handleBookmark}>
            <Bookmark
              size={20}
              color={isBookmarked ? colors.primary : colors.text}
              fill={isBookmarked ? colors.primary : 'transparent'}
            />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {article.urlToImage && (
          <View style={styles.imageContainer}>
            <Image
              source={{ uri: article.urlToImage }}
              style={styles.image}
              resizeMode="cover"
            />
          </View>
        )}

        <View style={styles.content}>
          <Text style={styles.title}>{article.title}</Text>

          <View style={styles.metadata}>
            <View style={styles.metadataItem}>
              <Clock size={14} color={colors.textSecondary} />
              <Text style={styles.metadataText}>
                {formatDate(article.publishedAt)}
              </Text>
            </View>
            {article.author && (
              <View style={styles.metadataItem}>
                <User size={14} color={colors.textSecondary} />
                <Text style={styles.metadataText}>{article.author}</Text>
              </View>
            )}
          </View>

          <Text style={styles.source}>{article.source.name}</Text>

          {article.description && (
            <Text style={styles.description}>{article.description}</Text>
          )}

          {article.content && (
            <Text style={styles.content_text}>
              {article.content.replace(/\[\+\d+ chars\]$/, '...')}
            </Text>
          )}

          <View style={styles.readMoreContainer}>
            <Text style={styles.readMoreTitle}>Read Full Article</Text>
            <Text style={styles.readMoreDescription}>
              Continue reading this story on the original source website for the complete article.
            </Text>
            <TouchableOpacity style={styles.readMoreButton} onPress={handleOpenOriginal}>
              <Text style={styles.readMoreButtonText}>Open Original Article</Text>
              <ExternalLink size={16} color="#ffffff" />
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}