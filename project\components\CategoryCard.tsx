import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { NewsCategory } from '@/types/news';
import { useTheme } from '@/contexts/ThemeContext';
import * as Icons from 'lucide-react-native';

interface CategoryCardProps {
  category: NewsCategory;
  onPress: () => void;
  isSelected?: boolean;
}

export function CategoryCard({ category, onPress, isSelected = false }: CategoryCardProps) {
  const { colors } = useTheme();
  
  // Get the icon component dynamically
  const IconComponent = (Icons as any)[category.icon] || Icons.Newspaper;

  const styles = StyleSheet.create({
    card: {
      backgroundColor: isSelected ? colors.primary : colors.surface,
      borderRadius: 12,
      padding: 16,
      marginHorizontal: 8,
      marginVertical: 4,
      minWidth: 120,
      alignItems: 'center',
      shadowColor: colors.text,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    iconContainer: {
      marginBottom: 8,
    },
    name: {
      fontSize: 14,
      fontWeight: '600',
      color: isSelected ? '#ffffff' : colors.text,
      textAlign: 'center',
    },
  });

  return (
    <TouchableOpacity style={styles.card} onPress={onPress} activeOpacity={0.7}>
      <View style={styles.iconContainer}>
        <IconComponent
          size={24}
          color={isSelected ? '#ffffff' : colors.primary}
        />
      </View>
      <Text style={styles.name}>{category.name}</Text>
    </TouchableOpacity>
  );
}