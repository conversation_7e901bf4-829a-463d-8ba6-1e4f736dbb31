import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { router } from 'expo-router';
import { BookmarkX } from 'lucide-react-native';
import { NewsCard } from '@/components/NewsCard';
import { StorageService } from '@/services/storage';
import { BookmarkedArticle } from '@/types/news';
import { useTheme } from '@/contexts/ThemeContext';

export default function BookmarksScreen() {
  const [bookmarkedArticles, setBookmarkedArticles] = useState<BookmarkedArticle[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const { colors } = useTheme();

  useEffect(() => {
    loadBookmarks();
  }, []);

  const loadBookmarks = async () => {
    const bookmarks = await StorageService.getBookmarks();
    setBookmarkedArticles(bookmarks);
    setRefreshing(false);
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadBookmarks();
  };

  const handleRemoveBookmark = async (articleUrl: string) => {
    await StorageService.removeBookmark(articleUrl);
    setBookmarkedArticles(prev => prev.filter(article => article.url !== articleUrl));
  };

  const handleArticlePress = (article: BookmarkedArticle) => {
    router.push({
      pathname: '/article/[id]',
      params: { 
        id: encodeURIComponent(article.url),
        article: JSON.stringify(article),
      },
    });
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      paddingHorizontal: 16,
      paddingTop: 16,
      paddingBottom: 8,
    },
    title: {
      fontSize: 32,
      fontFamily: 'Playfair-Bold',
      color: colors.text,
      marginBottom: 4,
    },
    subtitle: {
      fontSize: 16,
      fontFamily: 'Inter-Regular',
      color: colors.textSecondary,
      marginBottom: 16,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 32,
    },
    emptyIcon: {
      marginBottom: 16,
    },
    emptyTitle: {
      fontSize: 24,
      fontFamily: 'Playfair-Bold',
      color: colors.text,
      textAlign: 'center',
      marginBottom: 8,
    },
    emptyText: {
      fontSize: 16,
      fontFamily: 'Inter-Regular',
      color: colors.textSecondary,
      textAlign: 'center',
      lineHeight: 24,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Bookmarks</Text>
        <Text style={styles.subtitle}>
          {bookmarkedArticles.length} saved article{bookmarkedArticles.length !== 1 ? 's' : ''}
        </Text>
      </View>

      {bookmarkedArticles.length === 0 ? (
        <View style={styles.emptyContainer}>
          <BookmarkX size={64} color={colors.textSecondary} style={styles.emptyIcon} />
          <Text style={styles.emptyTitle}>No Bookmarks Yet</Text>
          <Text style={styles.emptyText}>
            Start saving articles you want to read later. Tap the bookmark icon on any article to save it here.
          </Text>
        </View>
      ) : (
        <ScrollView
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor={colors.primary}
              colors={[colors.primary]}
            />
          }
        >
          {bookmarkedArticles.map((article, index) => (
            <NewsCard
              key={`${article.url}-${index}`}
              article={article}
              onPress={() => handleArticlePress(article)}
              onBookmark={() => handleRemoveBookmark(article.url)}
              isBookmarked={true}
            />
          ))}
        </ScrollView>
      )}
    </SafeAreaView>
  );
}