import { NewsResponse } from '@/types/news';

const API_KEY = process.env.EXPO_PUBLIC_NEWS_API_KEY;
const BASE_URL = process.env.EXPO_PUBLIC_NEWS_API_URL;

export class NewsApiService {
  private static async fetchNews(endpoint: string): Promise<NewsResponse> {
    try {
      const response = await fetch(`${BASE_URL}${endpoint}&apiKey=${API_KEY}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('News API Error:', error);
      throw error;
    }
  }

  static async getTopHeadlines(country: string = 'us'): Promise<NewsResponse> {
    return this.fetchNews(`/top-headlines?country=${country}&pageSize=20`);
  }

  static async getNewsByCategory(category: string, country: string = 'us'): Promise<NewsResponse> {
    return this.fetchNews(`/top-headlines?country=${country}&category=${category}&pageSize=20`);
  }

  static async searchNews(query: string): Promise<NewsResponse> {
    const encodedQuery = encodeURIComponent(query);
    return this.fetchNews(`/everything?q=${encodedQuery}&sortBy=publishedAt&pageSize=20`);
  }
}