import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Linking,
} from 'react-native';
import { 
  Moon, 
  Sun, 
  Globe, 
  Info, 
  Mail, 
  Star, 
  Shield,
  ExternalLink,
  ChevronRight,
} from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';

export default function SettingsScreen() {
  const { theme, toggleTheme, colors } = useTheme();

  const handleEmailSupport = () => {
    Linking.openURL('mailto:<EMAIL>?subject=InfoPulse Support');
  };

  const handleRateApp = () => {
    // In a real app, this would open the app store
    console.log('Rate app');
  };

  const handlePrivacyPolicy = () => {
    Linking.openURL('https://infopulse.app/privacy');
  };

  const handleTermsOfService = () => {
    Linking.openURL('https://infopulse.app/terms');
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      paddingHorizontal: 16,
      paddingTop: 16,
      paddingBottom: 8,
    },
    title: {
      fontSize: 32,
      fontFamily: 'Playfair-Bold',
      color: colors.text,
      marginBottom: 4,
    },
    subtitle: {
      fontSize: 16,
      fontFamily: 'Inter-Regular',
      color: colors.textSecondary,
      marginBottom: 16,
    },
    section: {
      marginBottom: 32,
    },
    sectionTitle: {
      fontSize: 18,
      fontFamily: 'Inter-SemiBold',
      color: colors.text,
      marginBottom: 16,
      paddingHorizontal: 16,
    },
    settingItem: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.surface,
      paddingVertical: 16,
      paddingHorizontal: 16,
      marginHorizontal: 16,
      marginVertical: 4,
      borderRadius: 12,
    },
    settingIcon: {
      marginRight: 16,
    },
    settingContent: {
      flex: 1,
    },
    settingTitle: {
      fontSize: 16,
      fontFamily: 'Inter-Medium',
      color: colors.text,
      marginBottom: 2,
    },
    settingDescription: {
      fontSize: 14,
      fontFamily: 'Inter-Regular',
      color: colors.textSecondary,
    },
    settingValue: {
      fontSize: 14,
      fontFamily: 'Inter-Medium',
      color: colors.primary,
      marginRight: 8,
    },
    chevron: {
      marginLeft: 8,
    },
    themeToggle: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.border,
      borderRadius: 20,
      padding: 2,
    },
    themeOption: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 18,
    },
    activeThemeOption: {
      backgroundColor: colors.primary,
    },
    themeOptionText: {
      fontSize: 14,
      fontFamily: 'Inter-Medium',
      marginLeft: 6,
    },
    activeThemeOptionText: {
      color: '#ffffff',
    },
    inactiveThemeOptionText: {
      color: colors.textSecondary,
    },
    appInfo: {
      alignItems: 'center',
      paddingVertical: 32,
      paddingHorizontal: 16,
    },
    appName: {
      fontSize: 24,
      fontFamily: 'Playfair-Bold',
      color: colors.text,
      marginBottom: 4,
    },
    appVersion: {
      fontSize: 14,
      fontFamily: 'Inter-Regular',
      color: colors.textSecondary,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Settings</Text>
        <Text style={styles.subtitle}>Customize your news experience</Text>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Appearance</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingIcon}>
              {theme === 'light' ? (
                <Sun size={24} color={colors.primary} />
              ) : (
                <Moon size={24} color={colors.primary} />
              )}
            </View>
            <View style={styles.settingContent}>
              <Text style={styles.settingTitle}>Theme</Text>
              <Text style={styles.settingDescription}>Choose your preferred theme</Text>
            </View>
            <View style={styles.themeToggle}>
              <TouchableOpacity
                style={[
                  styles.themeOption,
                  theme === 'light' && styles.activeThemeOption,
                ]}
                onPress={theme === 'dark' ? toggleTheme : undefined}
              >
                <Sun size={16} color={theme === 'light' ? '#ffffff' : colors.textSecondary} />
                <Text
                  style={[
                    styles.themeOptionText,
                    theme === 'light' ? styles.activeThemeOptionText : styles.inactiveThemeOptionText,
                  ]}
                >
                  Light
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.themeOption,
                  theme === 'dark' && styles.activeThemeOption,
                ]}
                onPress={theme === 'light' ? toggleTheme : undefined}
              >
                <Moon size={16} color={theme === 'dark' ? '#ffffff' : colors.textSecondary} />
                <Text
                  style={[
                    styles.themeOptionText,
                    theme === 'dark' ? styles.activeThemeOptionText : styles.inactiveThemeOptionText,
                  ]}
                >
                  Dark
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Support</Text>
          
          <TouchableOpacity style={styles.settingItem} onPress={handleEmailSupport}>
            <Mail size={24} color={colors.primary} style={styles.settingIcon} />
            <View style={styles.settingContent}>
              <Text style={styles.settingTitle}>Contact Support</Text>
              <Text style={styles.settingDescription}>Get help with the app</Text>
            </View>
            <ExternalLink size={20} color={colors.textSecondary} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.settingItem} onPress={handleRateApp}>
            <Star size={24} color={colors.primary} style={styles.settingIcon} />
            <View style={styles.settingContent}>
              <Text style={styles.settingTitle}>Rate InfoPulse</Text>
              <Text style={styles.settingDescription}>Help us improve the app</Text>
            </View>
            <ChevronRight size={20} color={colors.textSecondary} style={styles.chevron} />
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Legal</Text>
          
          <TouchableOpacity style={styles.settingItem} onPress={handlePrivacyPolicy}>
            <Shield size={24} color={colors.primary} style={styles.settingIcon} />
            <View style={styles.settingContent}>
              <Text style={styles.settingTitle}>Privacy Policy</Text>
              <Text style={styles.settingDescription}>How we protect your data</Text>
            </View>
            <ExternalLink size={20} color={colors.textSecondary} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.settingItem} onPress={handleTermsOfService}>
            <Info size={24} color={colors.primary} style={styles.settingIcon} />
            <View style={styles.settingContent}>
              <Text style={styles.settingTitle}>Terms of Service</Text>
              <Text style={styles.settingDescription}>App usage terms</Text>
            </View>
            <ExternalLink size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>

        <View style={styles.appInfo}>
          <Text style={styles.appName}>InfoPulse</Text>
          <Text style={styles.appVersion}>Version 1.0.0</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}