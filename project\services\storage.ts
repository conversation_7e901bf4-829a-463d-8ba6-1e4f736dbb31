import AsyncStorage from '@react-native-async-storage/async-storage';
import { BookmarkedArticle } from '@/types/news';

const BOOKMARKS_KEY = 'bookmarked_articles';
const THEME_KEY = 'app_theme';

export class StorageService {
  static async getBookmarks(): Promise<BookmarkedArticle[]> {
    try {
      const bookmarks = await AsyncStorage.getItem(BOOKMARKS_KEY);
      return bookmarks ? JSON.parse(bookmarks) : [];
    } catch (error) {
      console.error('Error getting bookmarks:', error);
      return [];
    }
  }

  static async addBookmark(article: BookmarkedArticle): Promise<void> {
    try {
      const bookmarks = await this.getBookmarks();
      const updatedBookmarks = [article, ...bookmarks];
      await AsyncStorage.setItem(BOOKMARKS_KEY, JSON.stringify(updatedBookmarks));
    } catch (error) {
      console.error('Error adding bookmark:', error);
    }
  }

  static async removeBookmark(articleUrl: string): Promise<void> {
    try {
      const bookmarks = await this.getBookmarks();
      const updatedBookmarks = bookmarks.filter(bookmark => bookmark.url !== articleUrl);
      await AsyncStorage.setItem(BOOKMARKS_KEY, JSON.stringify(updatedBookmarks));
    } catch (error) {
      console.error('Error removing bookmark:', error);
    }
  }

  static async isBookmarked(articleUrl: string): Promise<boolean> {
    try {
      const bookmarks = await this.getBookmarks();
      return bookmarks.some(bookmark => bookmark.url === articleUrl);
    } catch (error) {
      console.error('Error checking bookmark:', error);
      return false;
    }
  }

  static async getTheme(): Promise<'light' | 'dark'> {
    try {
      const theme = await AsyncStorage.getItem(THEME_KEY);
      return (theme as 'light' | 'dark') || 'light';
    } catch (error) {
      console.error('Error getting theme:', error);
      return 'light';
    }
  }

  static async setTheme(theme: 'light' | 'dark'): Promise<void> {
    try {
      await AsyncStorage.setItem(THEME_KEY, theme);
    } catch (error) {
      console.error('Error setting theme:', error);
    }
  }
}