import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { router } from 'expo-router';
import { CategoryCard } from '@/components/CategoryCard';
import { NewsCard } from '@/components/NewsCard';
import { LoadingCard } from '@/components/LoadingCard';
import { NEWS_CATEGORIES } from '@/constants/categories';
import { NewsApiService } from '@/services/newsApi';
import { StorageService } from '@/services/storage';
import { NewsArticle } from '@/types/news';
import { useTheme } from '@/contexts/ThemeContext';

export default function CategoriesScreen() {
  const [selectedCategory, setSelectedCategory] = useState('general');
  const [articles, setArticles] = useState<NewsArticle[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [bookmarkedArticles, setBookmarkedArticles] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const { colors } = useTheme();

  useEffect(() => {
    loadCategoryNews();
    loadBookmarks();
  }, [selectedCategory]);

  const loadCategoryNews = async () => {
    try {
      setError(null);
      setLoading(true);
      const response = await NewsApiService.getNewsByCategory(selectedCategory);
      setArticles(response.articles);
    } catch (error) {
      console.error('Error loading category news:', error);
      setError('Failed to load news. Please check your internet connection and try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const loadBookmarks = async () => {
    const bookmarks = await StorageService.getBookmarks();
    setBookmarkedArticles(bookmarks.map(bookmark => bookmark.url));
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadCategoryNews();
  };

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
  };

  const handleBookmark = async (article: NewsArticle) => {
    const isBookmarked = bookmarkedArticles.includes(article.url);
    
    if (isBookmarked) {
      await StorageService.removeBookmark(article.url);
      setBookmarkedArticles(prev => prev.filter(url => url !== article.url));
    } else {
      const bookmarkedArticle = {
        ...article,
        bookmarkedAt: new Date().toISOString(),
      };
      await StorageService.addBookmark(bookmarkedArticle);
      setBookmarkedArticles(prev => [...prev, article.url]);
    }
  };

  const handleArticlePress = (article: NewsArticle) => {
    router.push({
      pathname: '/article/[id]',
      params: { 
        id: encodeURIComponent(article.url),
        article: JSON.stringify(article),
      },
    });
  };

  const selectedCategoryName = NEWS_CATEGORIES.find(cat => cat.id === selectedCategory)?.name || 'General';

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      paddingHorizontal: 16,
      paddingTop: 16,
      paddingBottom: 8,
    },
    title: {
      fontSize: 32,
      fontFamily: 'Playfair-Bold',
      color: colors.text,
      marginBottom: 4,
    },
    subtitle: {
      fontSize: 16,
      fontFamily: 'Inter-Regular',
      color: colors.textSecondary,
      marginBottom: 16,
    },
    categoriesContainer: {
      paddingVertical: 8,
    },
    categoriesScrollView: {
      paddingHorizontal: 8,
    },
    errorContainer: {
      backgroundColor: colors.error,
      marginHorizontal: 16,
      marginVertical: 8,
      padding: 16,
      borderRadius: 12,
    },
    errorText: {
      color: '#ffffff',
      fontSize: 14,
      fontFamily: 'Inter-Medium',
      textAlign: 'center',
    },
    loadingContainer: {
      paddingVertical: 16,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 32,
    },
    emptyText: {
      fontSize: 18,
      fontFamily: 'Inter-Medium',
      color: colors.textSecondary,
      textAlign: 'center',
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Categories</Text>
        <Text style={styles.subtitle}>{selectedCategoryName} News</Text>
      </View>

      <View style={styles.categoriesContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesScrollView}
        >
          {NEWS_CATEGORIES.map((category) => (
            <CategoryCard
              key={category.id}
              category={category}
              onPress={() => handleCategorySelect(category.id)}
              isSelected={selectedCategory === category.id}
            />
          ))}
        </ScrollView>
      </View>

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={colors.primary}
            colors={[colors.primary]}
          />
        }
      >
        {loading ? (
          <View style={styles.loadingContainer}>
            {[...Array(5)].map((_, index) => (
              <LoadingCard key={index} />
            ))}
          </View>
        ) : articles.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No articles available in this category.</Text>
          </View>
        ) : (
          articles.map((article, index) => (
            <NewsCard
              key={`${article.url}-${index}`}
              article={article}
              onPress={() => handleArticlePress(article)}
              onBookmark={() => handleBookmark(article)}
              isBookmarked={bookmarkedArticles.includes(article.url)}
            />
          ))
        )}
      </ScrollView>
    </SafeAreaView>
  );
}