import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { router } from 'expo-router';
import { NewsCard } from '@/components/NewsCard';
import { LoadingCard } from '@/components/LoadingCard';
import { SearchBar } from '@/components/SearchBar';
import { NewsApiService } from '@/services/newsApi';
import { StorageService } from '@/services/storage';
import { NewsArticle } from '@/types/news';
import { useTheme } from '@/contexts/ThemeContext';

export default function HomeScreen() {
  const [articles, setArticles] = useState<NewsArticle[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [bookmarkedArticles, setBookmarkedArticles] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const { colors } = useTheme();

  useEffect(() => {
    loadNews();
    loadBookmarks();
  }, []);

  useEffect(() => {
    if (searchQuery.trim()) {
      searchNews();
    } else {
      loadNews();
    }
  }, [searchQuery]);

  const loadNews = async () => {
    try {
      setError(null);
      const response = await NewsApiService.getTopHeadlines();
      setArticles(response.articles);
    } catch (error) {
      console.error('Error loading news:', error);
      setError('Failed to load news. Please check your internet connection and try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const searchNews = async () => {
    try {
      setError(null);
      setLoading(true);
      const response = await NewsApiService.searchNews(searchQuery);
      setArticles(response.articles);
    } catch (error) {
      console.error('Error searching news:', error);
      setError('Failed to search news. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadBookmarks = async () => {
    const bookmarks = await StorageService.getBookmarks();
    setBookmarkedArticles(bookmarks.map(bookmark => bookmark.url));
  };

  const handleRefresh = () => {
    setRefreshing(true);
    if (searchQuery.trim()) {
      searchNews();
    } else {
      loadNews();
    }
  };

  const handleBookmark = async (article: NewsArticle) => {
    const isBookmarked = bookmarkedArticles.includes(article.url);
    
    if (isBookmarked) {
      await StorageService.removeBookmark(article.url);
      setBookmarkedArticles(prev => prev.filter(url => url !== article.url));
    } else {
      const bookmarkedArticle = {
        ...article,
        bookmarkedAt: new Date().toISOString(),
      };
      await StorageService.addBookmark(bookmarkedArticle);
      setBookmarkedArticles(prev => [...prev, article.url]);
    }
  };

  const handleArticlePress = (article: NewsArticle) => {
    router.push({
      pathname: '/article/[id]',
      params: { 
        id: encodeURIComponent(article.url),
        article: JSON.stringify(article),
      },
    });
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      paddingHorizontal: 16,
      paddingTop: 16,
      paddingBottom: 8,
    },
    title: {
      fontSize: 32,
      fontFamily: 'Playfair-Bold',
      color: colors.text,
      marginBottom: 4,
    },
    subtitle: {
      fontSize: 16,
      fontFamily: 'Inter-Regular',
      color: colors.textSecondary,
      marginBottom: 16,
    },
    errorContainer: {
      backgroundColor: colors.error,
      marginHorizontal: 16,
      marginVertical: 8,
      padding: 16,
      borderRadius: 12,
    },
    errorText: {
      color: '#ffffff',
      fontSize: 14,
      fontFamily: 'Inter-Medium',
      textAlign: 'center',
    },
    loadingContainer: {
      paddingVertical: 16,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 32,
    },
    emptyText: {
      fontSize: 18,
      fontFamily: 'Inter-Medium',
      color: colors.textSecondary,
      textAlign: 'center',
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>InfoPulse</Text>
        <Text style={styles.subtitle}>
          {searchQuery ? `Search results for "${searchQuery}"` : 'Latest Headlines'}
        </Text>
      </View>

      <SearchBar
        value={searchQuery}
        onChangeText={setSearchQuery}
        onClear={() => setSearchQuery('')}
      />

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={colors.primary}
            colors={[colors.primary]}
          />
        }
      >
        {loading ? (
          <View style={styles.loadingContainer}>
            {[...Array(5)].map((_, index) => (
              <LoadingCard key={index} />
            ))}
          </View>
        ) : articles.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              {searchQuery ? 'No articles found for your search.' : 'No articles available.'}
            </Text>
          </View>
        ) : (
          articles.map((article, index) => (
            <NewsCard
              key={`${article.url}-${index}`}
              article={article}
              onPress={() => handleArticlePress(article)}
              onBookmark={() => handleBookmark(article)}
              isBookmarked={bookmarkedArticles.includes(article.url)}
            />
          ))
        )}
      </ScrollView>
    </SafeAreaView>
  );
}