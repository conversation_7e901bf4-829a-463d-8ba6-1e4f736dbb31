import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';

export function LoadingCard() {
  const { colors } = useTheme();

  const styles = StyleSheet.create({
    card: {
      backgroundColor: colors.surface,
      borderRadius: 16,
      marginHorizontal: 16,
      marginVertical: 8,
      overflow: 'hidden',
    },
    imagePlaceholder: {
      height: 200,
      backgroundColor: colors.border,
    },
    content: {
      padding: 16,
    },
    titlePlaceholder: {
      height: 20,
      backgroundColor: colors.border,
      borderRadius: 4,
      marginBottom: 8,
    },
    titlePlaceholderShort: {
      height: 20,
      backgroundColor: colors.border,
      borderRadius: 4,
      width: '70%',
      marginBottom: 12,
    },
    descriptionPlaceholder: {
      height: 14,
      backgroundColor: colors.border,
      borderRadius: 4,
      marginBottom: 4,
    },
    descriptionPlaceholderShort: {
      height: 14,
      backgroundColor: colors.border,
      borderRadius: 4,
      width: '80%',
      marginBottom: 12,
    },
    footerPlaceholder: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    sourcePlaceholder: {
      height: 12,
      width: 80,
      backgroundColor: colors.border,
      borderRadius: 4,
    },
    timePlaceholder: {
      height: 12,
      width: 60,
      backgroundColor: colors.border,
      borderRadius: 4,
    },
  });

  return (
    <View style={styles.card}>
      <View style={styles.imagePlaceholder} />
      <View style={styles.content}>
        <View style={styles.titlePlaceholder} />
        <View style={styles.titlePlaceholderShort} />
        <View style={styles.descriptionPlaceholder} />
        <View style={styles.descriptionPlaceholderShort} />
        <View style={styles.footerPlaceholder}>
          <View style={styles.sourcePlaceholder} />
          <View style={styles.timePlaceholder} />
        </View>
      </View>
    </View>
  );
}