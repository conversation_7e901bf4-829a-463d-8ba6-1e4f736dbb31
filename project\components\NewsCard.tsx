import React from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet } from 'react-native';
import { Bookmark, Clock, ExternalLink } from 'lucide-react-native';
import { NewsArticle } from '@/types/news';
import { useTheme } from '@/contexts/ThemeContext';

interface NewsCardProps {
  article: NewsArticle;
  onPress: () => void;
  onBookmark: () => void;
  isBookmarked: boolean;
}

export function NewsCard({ article, onPress, onBookmark, isBookmarked }: NewsCardProps) {
  const { colors } = useTheme();

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  const styles = StyleSheet.create({
    card: {
      backgroundColor: colors.surface,
      borderRadius: 16,
      marginHorizontal: 16,
      marginVertical: 8,
      shadowColor: colors.text,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 4,
      overflow: 'hidden',
    },
    imageContainer: {
      height: 200,
      backgroundColor: colors.border,
    },
    image: {
      width: '100%',
      height: '100%',
    },
    content: {
      padding: 16,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: 8,
    },
    title: {
      fontSize: 18,
      fontWeight: '700',
      color: colors.text,
      lineHeight: 24,
      flex: 1,
      marginRight: 12,
    },
    bookmarkButton: {
      padding: 4,
    },
    description: {
      fontSize: 14,
      color: colors.textSecondary,
      lineHeight: 20,
      marginBottom: 12,
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    source: {
      fontSize: 12,
      fontWeight: '600',
      color: colors.primary,
    },
    timeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    time: {
      fontSize: 12,
      color: colors.textSecondary,
      marginLeft: 4,
    },
  });

  return (
    <TouchableOpacity style={styles.card} onPress={onPress} activeOpacity={0.7}>
      {article.urlToImage && (
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: article.urlToImage }}
            style={styles.image}
            resizeMode="cover"
          />
        </View>
      )}
      
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title} numberOfLines={3}>
            {article.title}
          </Text>
          <TouchableOpacity style={styles.bookmarkButton} onPress={onBookmark}>
            <Bookmark
              size={20}
              color={isBookmarked ? colors.primary : colors.textSecondary}
              fill={isBookmarked ? colors.primary : 'transparent'}
            />
          </TouchableOpacity>
        </View>
        
        {article.description && (
          <Text style={styles.description} numberOfLines={2}>
            {article.description}
          </Text>
        )}
        
        <View style={styles.footer}>
          <Text style={styles.source}>{article.source.name}</Text>
          <View style={styles.timeContainer}>
            <Clock size={12} color={colors.textSecondary} />
            <Text style={styles.time}>{formatTimeAgo(article.publishedAt)}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
}